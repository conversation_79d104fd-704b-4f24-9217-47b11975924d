<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Potion;
use App\Models\UserPotion;
use App\Services\FlashMessageService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;

/**
 * Контроллер для работы с зельями
 */
class PotionController extends Controller
{
    /**
     * Сервис для работы с сообщениями
     */
    protected $flashMessageService;

    /**
     * Конструктор контроллера
     *
     * @param FlashMessageService $flashMessageService
     */
    public function __construct(FlashMessageService $flashMessageService)
    {
        $this->flashMessageService = $flashMessageService;
    }

    /**
     * Использование зелья
     *
     * @param Request $request
     * @param int $id ID зелья
     * @return \Illuminate\Http\RedirectResponse
     */
    public function usePotion(Request $request, $id)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->back()->with('error', 'Вы должны быть авторизованы.');
        }

        // Защита от спама использования зелий
        $limiterKey = "use-potion:{$user->id}";
        $maxAttempts = 3; // Максимум 3 попытки
        $decaySeconds = 5; // За 5 секунд

        if (RateLimiter::tooManyAttempts($limiterKey, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($limiterKey);
            return redirect()->back()->with('error', "Слишком быстро! Подождите {$seconds} сек.");
        }

        RateLimiter::hit($limiterKey, $decaySeconds);

        // Сначала ищем в новой таблице user_potions
        $potion = UserPotion::where('id', $id)
            ->where('user_id', $user->id)
            ->where('location', UserPotion::LOCATION_INVENTORY)
            ->first();

        $isNewModel = true;

        // Если не найдено, ищем в старой таблице potions
        if (!$potion) {
            $potion = Potion::where('id', $id)
                ->where('user_id', $user->id)
                ->where('location', 'inventory')
                ->where('uses_left', '>', 0)
                ->first();

            $isNewModel = false;
        }

        if (!$potion) {
            return redirect()->back()->with('error', 'Зелье не найдено или не может быть использовано.');
        }

        // Получаем актуальные ресурсы пользователя
        $actualResources = $user->profile->getActualResources();

        // Применяем эффект зелья
        $effectApplied = false;
        $effectMessage = '';

        switch ($potion->effect) {
            case 'health':
                // Проверяем наличие ключа max_hp в массиве ресурсов
                $maxHp = $actualResources['max_hp'] ?? $user->profile->max_hp;

                // Проверяем, не полное ли уже здоровье
                if ($actualResources['current_hp'] >= $maxHp) {
                    return redirect()->back()->with('info', 'У вас уже полное здоровье.');
                }

                // Получаем значение эффекта
                $effectValue = $isNewModel && method_exists($potion, 'getActualEffectValue')
                    ? $potion->getActualEffectValue()
                    : $potion->effect_value;

                // Применяем эффект восстановления здоровья
                $newHp = min($actualResources['current_hp'] + $effectValue, $maxHp);
                $user->profile->setRedisHp($newHp);

                $effectApplied = true;
                $effectMessage = "Восстановлено {$effectValue} HP.";
                break;

            case 'mana':
                // Проверяем наличие ключа max_mp в массиве ресурсов
                $maxMp = $actualResources['max_mp'] ?? $user->profile->max_mp;

                // Проверяем, не полная ли уже мана
                if ($actualResources['current_mp'] >= $maxMp) {
                    return redirect()->back()->with('info', 'У вас уже полная мана.');
                }

                // Получаем значение эффекта
                $effectValue = $isNewModel && method_exists($potion, 'getActualEffectValue')
                    ? $potion->getActualEffectValue()
                    : $potion->effect_value;

                // Применяем эффект восстановления маны
                $newMp = min($actualResources['current_mp'] + $effectValue, $maxMp);
                $user->profile->setRedisMp($newMp);

                $effectApplied = true;
                $effectMessage = "Восстановлено {$effectValue} MP.";
                break;

            // Другие эффекты зелий можно добавить здесь
            case 'strength':
            case 'agility':
            case 'intelligence':
            case 'stamina':
                // Временное увеличение характеристик через эффекты
                // Здесь можно добавить логику для создания временного эффекта
                $effectApplied = true;
                $effectMessage = "Применен эффект {$potion->effect}.";
                break;

            default:
                Log::warning("Неизвестный эффект зелья: {$potion->effect}", [
                    'potion_id' => $potion->id,
                    'user_id' => $user->id
                ]);
                return redirect()->back()->with('error', 'Неизвестный эффект зелья.');
        }

        if ($effectApplied) {
            // Уменьшаем количество использований или удаляем зелье
            if ($isNewModel) {
                // Для новой модели
                if ($potion->quantity > 1) {
                    $potion->decrement('quantity');
                } else {
                    $potion->delete();
                }
            } else {
                // Для старой модели
                if ($potion->uses_left > 1) {
                    $potion->decrement('uses_left');
                } else {
                    $potion->delete();
                }
            }

            // Логируем использование зелья
            Log::info("Игрок {$user->name} использовал зелье {$potion->name}", [
                'potion_id' => $potion->id,
                'user_id' => $user->id,
                'effect' => $potion->effect,
                'effect_value' => $effectValue ?? null
            ]);

            // Возвращаемся на предыдущую страницу с сообщением об успехе
            return redirect()->back()->with('success', "Вы использовали {$potion->name}. {$effectMessage}")
                ->with('potion_used', [
                    'name' => $potion->name,
                    'effect' => $effectMessage
                ]);
        }

        // Если эффект не был применен
        return redirect()->back()->with('error', 'Не удалось применить эффект зелья.');
    }
}
