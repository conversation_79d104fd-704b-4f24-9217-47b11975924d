/**
 * Система уведомлений для зелий
 * Отображает красивые уведомления по центру экрана в стиле алхимического стола
 */

class PotionNotifications {
    constructor() {
        this.container = null;
        this.defaultDuration = 3000; // 3 секунды
        this.recentNotifications = new Map(); // Для предотвращения дублирования
        this.init();
    }

    /**
     * Инициализация системы уведомлений
     */
    init() {
        this.createContainer();
        console.log('🧪 Система уведомлений зелий инициализирована');
    }

    /**
     * Создание контейнера для уведомлений
     */
    createContainer() {
        // Проверяем, не существует ли уже контейнер
        if (document.getElementById('potion-notifications-container')) {
            this.container = document.getElementById('potion-notifications-container');
            return;
        }

        this.container = document.createElement('div');
        this.container.id = 'potion-notifications-container';
        this.container.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 pointer-events-none';
        this.container.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            pointer-events: none;
            max-width: 90vw;
            text-align: center;
        `;

        document.body.appendChild(this.container);
    }

    /**
     * Показать уведомление о зелье
     * @param {string} message - Текст уведомления
     * @param {string} type - Тип уведомления (success, error, warning, info, brewing, ready)
     * @param {number} duration - Длительность показа (необязательно)
     */
    show(message, type = 'info', duration = null) {
        // Предотвращаем дублирование уведомлений
        const notificationKey = `${message}-${type}`;
        const currentTime = Date.now();
        
        if (this.recentNotifications.has(notificationKey)) {
            const lastTime = this.recentNotifications.get(notificationKey);
            if (currentTime - lastTime < 1000) { // Блокируем дубли в течение 1 секунды
                return;
            }
        }

        this.recentNotifications.set(notificationKey, currentTime);
        this.cleanupRecentNotifications();

        console.log(`🧪 Показываем уведомление о зелье: "${message}" (${type})`);

        duration = duration || this.defaultDuration;

        // Создаем элемент уведомления
        const notification = this.createNotificationElement(message, type);

        // Добавляем в контейнер
        this.container.appendChild(notification);

        // Анимация появления
        this.animateIn(notification);

        // Автоматическое удаление
        setTimeout(() => {
            this.animateOut(notification);
        }, duration);
    }

    /**
     * Создание элемента уведомления
     */
    createNotificationElement(message, type) {
        const notification = document.createElement('div');
        notification.className = 'potion-notification mb-2';
        
        // Базовые стили в стиле алхимического стола
        const baseStyles = `
            display: inline-block;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border: 1px solid;
            backdrop-filter: blur(4px);
            max-width: 400px;
            word-wrap: break-word;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
            transform: translateY(-20px);
            opacity: 0;
            transition: all 0.3s ease-out;
        `;

        // Стили для разных типов уведомлений в стиле игры
        const typeStyles = {
            success: `
                background: linear-gradient(135deg, #36513f 0%, #2a3f32 100%);
                color: #c8ffdb;
                border-color: #4a7759;
            `,
            error: `
                background: linear-gradient(135deg, #613f36 0%, #4a2e26 100%);
                color: #ffeac1;
                border-color: #88634a;
            `,
            warning: `
                background: linear-gradient(135deg, #5e553a 0%, #4a452c 100%);
                color: #ffe7bd;
                border-color: #7d7248;
            `,
            info: `
                background: linear-gradient(135deg, #3a4a5e 0%, #2d3a4a 100%);
                color: #c1dcff;
                border-color: #4a617d;
            `,
            brewing: `
                background: linear-gradient(135deg, #4a452c 0%, #3a3521 100%);
                color: #e5b769;
                border-color: #8c784e;
            `,
            ready: `
                background: linear-gradient(135deg, #1a301a 0%, #0f1f0f 100%);
                color: #a6e269;
                border-color: #397239;
            `
        };

        notification.style.cssText = baseStyles + (typeStyles[type] || typeStyles.info);

        // Иконки для разных типов
        const icons = {
            success: "✅",
            error: "❌", 
            warning: "⚠️",
            info: "ℹ️",
            brewing: "🧪",
            ready: "✨"
        };

        notification.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                <span style="font-size: 16px;">${icons[type] || icons.info}</span>
                <span>${this.escapeHtml(message)}</span>
            </div>
        `;

        return notification;
    }

    /**
     * Анимация появления
     */
    animateIn(element) {
        // Используем requestAnimationFrame для плавной анимации
        requestAnimationFrame(() => {
            element.style.transform = 'translateY(0)';
            element.style.opacity = '1';
        });
    }

    /**
     * Анимация исчезновения
     */
    animateOut(element) {
        element.style.transform = 'translateY(-20px)';
        element.style.opacity = '0';
        
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 300);
    }

    /**
     * Очистка старых записей о недавних уведомлениях
     */
    cleanupRecentNotifications() {
        const currentTime = Date.now();
        for (const [key, time] of this.recentNotifications.entries()) {
            if (currentTime - time > 10000) { // Удаляем записи старше 10 секунд
                this.recentNotifications.delete(key);
            }
        }
    }

    /**
     * Экранирование HTML для безопасности
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Специальные методы для разных типов уведомлений зелий
     */
    
    // Уведомление об использовании зелья
    showPotionUsed(potionName, effect) {
        this.show(`Использовано: ${potionName}. ${effect}`, 'success');
    }

    // Уведомление о начале варки
    showBrewingStarted(recipeName, duration) {
        const minutes = Math.ceil(duration / 60);
        this.show(`Начата варка: ${recipeName} (${minutes} мин.)`, 'brewing');
    }

    // Уведомление о готовности зелья
    showPotionReady(potionName) {
        this.show(`Готово в котле: ${potionName}`, 'ready', 4000);
    }

    // Уведомление об ошибке с зельем
    showPotionError(message) {
        this.show(message, 'error');
    }

    // Уведомление о занятом алхимическом столе
    showTableBusy() {
        this.show('Алхимический стол уже занят', 'warning');
    }

    // Уведомление о том, что рецепт не выбран
    showNoRecipeSelected() {
        this.show('Рецепт не выбран', 'warning');
    }
}

// CSS стили для уведомлений
const style = document.createElement('style');
style.textContent = `
    .potion-notification {
        animation: potionNotificationFadeIn 0.3s ease-out forwards;
    }

    @keyframes potionNotificationFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Эффект при наведении (если уведомление интерактивное) */
    .potion-notification:hover {
        transform: scale(1.02);
        transition: transform 0.2s ease;
    }

    /* Адаптивные стили */
    @media (max-width: 640px) {
        .potion-notification {
            font-size: 12px;
            padding: 10px 16px;
            max-width: 300px;
        }
    }
`;
document.head.appendChild(style);

// Создаем глобальный экземпляр
window.PotionNotifications = new PotionNotifications();

// Экспортируем для использования в других модулях
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PotionNotifications;
}
