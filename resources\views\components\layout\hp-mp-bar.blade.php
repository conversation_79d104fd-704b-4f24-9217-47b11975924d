@props(['actualResources', 'userProfile'])

<div class="flex justify-between items-center text-[#d9d3b8] rounded-md pt-1.5 shadow-inner">
    {{-- HP с иконкой --}}
    <div class="flex items-center">
        <img src="{{ asset('assets/user/hpIcon.png') }}" alt="HP" class="w-6 h-6">
        <div class="flex flex-col">
            <!--
                Русский комментарий:
                Здесь отображается прогресс-бар HP (здоровья) персонажа.
                Вместо устаревших значений из базы используем актуальные значения из actualResources,
                которые рассчитываются на лету с учетом времени и восстановления.
            -->
            <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                <div class="bg-gradient-to-r from-[#8B0000] to-[#FF6347] h-full rounded-full hp-bar"
                    style="width: {{ $userProfile->max_hp > 0 ? (($actualResources['current_hp'] / $userProfile->max_hp) * 100) : 0 }}%">
                </div>
            </div>
            <span class="text-[#e5b769] text-[12px] hp-text">
                {{ $actualResources['current_hp'] ?? 0 }}/{{ $userProfile->max_hp ?? 100 }}
                <!--
                    Русский комментарий:
                    Отображаем актуальное количество HP (здоровья) и максимум.
                    Добавлена защита от деления на ноль и null значений.
                -->
            </span>
        </div>
    </div>

    {{-- Слот для дополнительного контента между HP и MP (например, уведомления) --}}
    {{ $slot }}

    {{-- Уведомления о зельях --}}
    <x-layout.potion-notifications />

    {{-- MP с иконкой --}}
    <div class="flex items-center">
        <div class="flex flex-col items-end">
            <!--
                Русский комментарий:
                Здесь отображается прогресс-бар MP (маны) персонажа.
                Используем актуальные значения из actualResources.
            -->
            <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                <div class="bg-gradient-to-r from-[#00008B] to-[#1E90FF] h-full rounded-full mp-bar"
                    style="width: {{ $userProfile->max_mp > 0 ? (($actualResources['current_mp'] / $userProfile->max_mp) * 100) : 0 }}%">
                </div>
            </div>
            <span class="text-[#e5b769] text-[12px] mp-text">
                {{ $actualResources['current_mp'] ?? 0 }}/{{ $userProfile->max_mp ?? 50 }}
                <!--
                    Русский комментарий:
                    Отображаем актуальное количество MP (маны) и максимум.
                    Добавлена защита от деления на ноль и null значений.
                -->
            </span>
        </div>
        <img src="{{ asset('assets/user/mp-icon.png') }}" alt="MP" class="w-6 h-6">
    </div>
</div>