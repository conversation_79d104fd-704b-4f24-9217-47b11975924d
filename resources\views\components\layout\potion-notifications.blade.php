{{--
Компонент уведомлений о зельях
Отображается между HP и MP полосками
--}}

@props(['potionUsed' => null])

{{-- Контейнер для уведомлений о зельях --}}
<div id="potion-notifications-area" class="w-full text-center py-1">
    {{-- Уведомления об использовании зелий --}}
    @if (session('potion_used'))
        @php
            $potionData = session('potion_used');
        @endphp
        <div id="potion-notification" class="inline-block bg-gradient-to-br from-[#36513f] to-[#2a3f32] text-[#c8ffdb] px-3 py-2 rounded-md border border-[#4a7759] shadow-lg animate-fade-in text-center text-xs"
            style="box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1); backdrop-filter: blur(4px); text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);">
            <div class="flex items-center justify-center gap-2">
                <span class="text-sm">🧪</span>
                <span class="font-medium">Использовано: {{ $potionData['name'] }}. {{ $potionData['effect'] }}</span>
            </div>
        </div>
        <script>
            // Автоматически скрываем уведомление через 2.5 секунды
            setTimeout(() => {
                const notification = document.getElementById('potion-notification');
                if (notification) {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, 2500);
        </script>
    @endif
</div>

{{-- Стили для анимации --}}
<style>
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeIn 0.3s ease-out forwards;
    }

    #potion-notification {
        transition: all 0.3s ease-out;
    }
</style>
