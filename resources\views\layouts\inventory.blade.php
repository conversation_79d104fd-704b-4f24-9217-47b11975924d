<!DOCTYPE html>
<html lang="ru">
@php use Illuminate\Support\Facades\Auth; @endphp {{-- Используем фасад Auth --}}

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Рюкзак - {{ Auth::check() ? Auth::user()->name : 'Гость' }}</title>

    {{-- Безопасная загрузка ассетов --}}
    <x-secure-assets />

    {{-- Подключение стилей для инвентаря --}}
    @vite(['resources/css/inventory.css', 'resources/js/potions/notifications.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif">
    {{-- Основной контейнер страницы --}}
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg shadow-lg overflow-hidden"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валюты --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

        {{-- Сообщения --}}
        <div class="text-center flex justify-center space-x-1">
            @if (session('welcome_message'))
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    {{ session('welcome_message') }}
                </div>
            @endif
        </div>

        {{-- Кнопка возврата в бой (если доступна) --}}
        @if(isset($canReturnToBattle) && $canReturnToBattle && isset($lastBattleRoute) && !empty($lastBattleRoute))
            <a href="{{ url($lastBattleRoute) }}" class="block w-full mb-0 max-w-xs mx-auto px-2 py-0.5 mt-0 text-lg font-semibold text-[#c4a46b]
                                                           bg-gradient-to-b from-[#2b2a21] to-[#1d1c17] border border-[#8c784e]
                                                           rounded-md uppercase tracking-wide text-center
                                                           hover:border-[#b59e70] hover:text-[#dfc590] hover:shadow-md
                                                           transition-all duration-300 magic-glow">
                ⚔️ ВЕРНУТЬСЯ В БОЙ ⚔️
            </a>
        @endif

        {{-- Блок изображения локации с флеш-сообщениями --}}
        <x-layout.location-image :breadcrumbs="$breadcrumbs" title="Рюкзак"
            imagePath="assets/bannersBg/inventoryBanner.png" imageAlt="Баннер инвентаря">

            {{-- Флеш-сообщения --}}
            <x-game-flash-messages />

            {{-- Ошибки валидации --}}
            @if ($errors->any())
                <div
                    class="bg-[#613f36] text-[#ffeac1] p-1.5 mx-2 my-1.5 text-xs rounded border border-[#88634a] shadow-inner shadow-black/30 mb-2 animate-fade-in">
                    <ul class="list-disc list-inside">
                        @foreach ($errors->all() as $error)
                            <li>{!! $error !!}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
        </x-layout.location-image>


        {{-- Отображение занятых/всего слотов рюкзака --}}
        <div class="flex items-center justify-center mb-0 mt-[-25px]">
            <span class="text-sm text-gray-300 font-mono mr-0.5">
                {{ $userProfile->inventory_used }}/{{ $userProfile->inventory_capacity }}
            </span>
            {{-- Прогресс-бар заполнения рюкзака --}}
            <div class="w-32 bg-gray-700 rounded-full h-2.5">
                <div class="bg-[#e5b769] h-2.5 rounded-full"
                    style="width: {{ $userProfile->inventory_capacity > 0 ? min(100, ($userProfile->inventory_used / $userProfile->inventory_capacity) * 100) : 0 }}%">
                </div>
            </div>
            <span class="text-xs text-gray-400 ml-0.5">слотов</span>
        </div>



        {{-- Основное содержимое --}}
        @yield('content')
    </div>

    {{-- Нижние кнопки навигации --}}
    <x-layout.navigation-buttons />

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" />

    {{-- Подключение скриптов --}}
    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/global/csrf.js', 'resources/js/layout/footer-counters.js', 'resources/js/layout/server-time.js'])

    {{-- Стек для дополнительных скриптов --}}
    @stack('scripts')
</body>

</html>