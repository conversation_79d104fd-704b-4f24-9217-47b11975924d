<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['actualResources', 'userProfile']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['actualResources', 'userProfile']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="flex justify-between items-center text-[#d9d3b8] rounded-md pt-1.5 shadow-inner">
    
    <div class="flex items-center">
        <img src="<?php echo e(asset('assets/user/hpIcon.png')); ?>" alt="HP" class="w-6 h-6">
        <div class="flex flex-col">
            <!--
                Русский комментарий:
                Здесь отображается прогресс-бар HP (здоровья) персонажа.
                Вместо устаревших значений из базы используем актуальные значения из actualResources,
                которые рассчитываются на лету с учетом времени и восстановления.
            -->
            <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                <div class="bg-gradient-to-r from-[#8B0000] to-[#FF6347] h-full rounded-full hp-bar"
                    style="width: <?php echo e($userProfile->max_hp > 0 ? (($actualResources['current_hp'] / $userProfile->max_hp) * 100) : 0); ?>%">
                </div>
            </div>
            <span class="text-[#e5b769] text-[12px] hp-text">
                <?php echo e($actualResources['current_hp'] ?? 0); ?>/<?php echo e($userProfile->max_hp ?? 100); ?>

                <!--
                    Русский комментарий:
                    Отображаем актуальное количество HP (здоровья) и максимум.
                    Добавлена защита от деления на ноль и null значений.
                -->
            </span>
        </div>
    </div>

    
    <?php echo e($slot); ?>


    
    <?php if (isset($component)) { $__componentOriginala59ce0ef6b3a4449d3fc561adb76ec4e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala59ce0ef6b3a4449d3fc561adb76ec4e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.potion-notifications','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.potion-notifications'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala59ce0ef6b3a4449d3fc561adb76ec4e)): ?>
<?php $attributes = $__attributesOriginala59ce0ef6b3a4449d3fc561adb76ec4e; ?>
<?php unset($__attributesOriginala59ce0ef6b3a4449d3fc561adb76ec4e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala59ce0ef6b3a4449d3fc561adb76ec4e)): ?>
<?php $component = $__componentOriginala59ce0ef6b3a4449d3fc561adb76ec4e; ?>
<?php unset($__componentOriginala59ce0ef6b3a4449d3fc561adb76ec4e); ?>
<?php endif; ?>

    
    <div class="flex items-center">
        <div class="flex flex-col items-end">
            <!--
                Русский комментарий:
                Здесь отображается прогресс-бар MP (маны) персонажа.
                Используем актуальные значения из actualResources.
            -->
            <div class="w-16 bg-[#3b3a33] h-1.5 rounded-full overflow-hidden">
                <div class="bg-gradient-to-r from-[#00008B] to-[#1E90FF] h-full rounded-full mp-bar"
                    style="width: <?php echo e($userProfile->max_mp > 0 ? (($actualResources['current_mp'] / $userProfile->max_mp) * 100) : 0); ?>%">
                </div>
            </div>
            <span class="text-[#e5b769] text-[12px] mp-text">
                <?php echo e($actualResources['current_mp'] ?? 0); ?>/<?php echo e($userProfile->max_mp ?? 50); ?>

                <!--
                    Русский комментарий:
                    Отображаем актуальное количество MP (маны) и максимум.
                    Добавлена защита от деления на ноль и null значений.
                -->
            </span>
        </div>
        <img src="<?php echo e(asset('assets/user/mp-icon.png')); ?>" alt="MP" class="w-6 h-6">
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/layout/hp-mp-bar.blade.php ENDPATH**/ ?>