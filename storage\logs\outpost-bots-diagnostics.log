🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 40MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.82мс)
   📊 Количество ключей в Redis: 81
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 137.85мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 55
   ✅ Активных ботов: 55
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 43
   🏰 Активных аванпостов: 4
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 137.85мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 42MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.17мс)
   📊 Количество ключей в Redis: 113
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 40.05мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 69
   ✅ Активных ботов: 69
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 62
   🏰 Активных аванпостов: 4
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 42MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.05мс)
   📊 Количество ключей в Redis: 118
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 40.79мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 69
   ✅ Активных ботов: 69
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 62
   🏰 Активных аванпостов: 4
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 9.33мс)
   📊 Количество ключей в Redis: 112
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 55.19мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 55.19мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.9мс)
   📊 Количество ключей в Redis: 119
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 51.11мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 51.11мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.58мс)
   📊 Количество ключей в Redis: 117
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 162.04мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 162.04мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.27мс)
   📊 Количество ключей в Redis: 111
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 43.42мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.64мс)
   📊 Количество ключей в Redis: 111
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 50.45мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 50.45мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.62мс)
   📊 Количество ключей в Redis: 116
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 154.93мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 154.93мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.3мс)
   📊 Количество ключей в Redis: 116
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 157.46мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 157.46мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.68мс)
   📊 Количество ключей в Redis: 115
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 52.02мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 52.02мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.09мс)
   📊 Количество ключей в Redis: 113
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 161.12мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 161.12мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.11мс)
   📊 Количество ключей в Redis: 118
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 54.15мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 54.15мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.86мс)
   📊 Количество ключей в Redis: 114
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 50.37мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 50.37мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.37мс)
   📊 Количество ключей в Redis: 100
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 40.45мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.54мс)
   📊 Количество ключей в Redis: 100
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 62.22мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 62.22мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.63мс)
   📊 Количество ключей в Redis: 100
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 64.41мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 64.41мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.04мс)
   📊 Количество ключей в Redis: 100
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 47.35мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5мс)
   📊 Количество ключей в Redis: 100
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 46.88мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.4мс)
   📊 Количество ключей в Redis: 100
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 45.45мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.27мс)
   📊 Количество ключей в Redis: 100
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 54.76мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 54.76мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.1мс)
   📊 Количество ключей в Redis: 100
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 56.08мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 56.08мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.16мс)
   📊 Количество ключей в Redis: 100
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 41.95мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.32мс)
   📊 Количество ключей в Redis: 115
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 55.97мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 55.97мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 34.68мс)
   📊 Количество ключей в Redis: 113
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 80.27мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 80.27мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.95мс)
   📊 Количество ключей в Redis: 116
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 61.46мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 61.46мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.11мс)
   📊 Количество ключей в Redis: 113
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 160.74мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 160.74мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.45мс)
   📊 Количество ключей в Redis: 96
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 46.27мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.54мс)
   📊 Количество ключей в Redis: 96
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 46.92мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 52MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.72мс)
   📊 Количество ключей в Redis: 95
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 48.13мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 54MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.36мс)
   📊 Количество ключей в Redis: 123
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 48.62мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 54MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.77мс)
   📊 Количество ключей в Redis: 133
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 56.33мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 56.33мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 54MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.67мс)
   📊 Количество ключей в Redis: 126
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 53.88мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 53.88мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 54MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.97мс)
   📊 Количество ключей в Redis: 118
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 65.74мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 44
   ✅ Активных ботов: 44
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 2
   🏰 Активных аванпостов: 6
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 65.74мс
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.15мс)
   📊 Количество ключей в Redis: 63
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 53.53мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 53.53мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.31мс)
   📊 Количество ключей в Redis: 65
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 42.34мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.27мс)
   📊 Количество ключей в Redis: 48
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 201.92мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 201.92мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.66мс)
   📊 Количество ключей в Redis: 48
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 62.41мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 62.41мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 20.95мс)
   📊 Количество ключей в Redis: 45
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 150.97мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 150.97мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.58мс)
   📊 Количество ключей в Redis: 46
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 61.41мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 61.41мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.89мс)
   📊 Количество ключей в Redis: 45
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 79.97мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 79.97мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 11.38мс)
   📊 Количество ключей в Redis: 176
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 63.38мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 63.38мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.31мс)
   📊 Количество ключей в Redis: 177
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 43.66мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 12.18мс)
   📊 Количество ключей в Redis: 178
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 67.32мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 67.32мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.07мс)
   📊 Количество ключей в Redis: 178
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 53.59мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 53.59мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.74мс)
   📊 Количество ключей в Redis: 173
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 54.01мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 54.01мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.11мс)
   📊 Количество ключей в Redis: 164
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 41.26мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.13мс)
   📊 Количество ключей в Redis: 173
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 50.22мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 50.22мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.06мс)
   📊 Количество ключей в Redis: 177
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 48.51мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.18мс)
   📊 Количество ключей в Redis: 173
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 60.32мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 60.32мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.05мс)
   📊 Количество ключей в Redis: 166
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 140.79мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 140.79мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.6мс)
   📊 Количество ключей в Redis: 173
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 46.19мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 54
   ✅ Активных ботов: 54
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 54
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.53мс)
   📊 Количество ключей в Redis: 175
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 40.08мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 55
   ✅ Активных ботов: 55
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 55
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.82мс)
   📊 Количество ключей в Redis: 179
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 149.33мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 56
   ✅ Активных ботов: 56
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 56
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 149.33мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.61мс)
   📊 Количество ключей в Redis: 177
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 150.04мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 56
   ✅ Активных ботов: 56
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 56
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 150.04мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.37мс)
   📊 Количество ключей в Redis: 175
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 51.63мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 56
   ✅ Активных ботов: 56
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 56
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 51.63мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.33мс)
   📊 Количество ключей в Redis: 188
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 46.42мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 57
   ✅ Активных ботов: 57
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 57
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.8мс)
   📊 Количество ключей в Redis: 203
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 54мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 57
   ✅ Активных ботов: 57
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 57
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 54мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 19.13мс)
   📊 Количество ключей в Redis: 192
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 62.5мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 57
   ✅ Активных ботов: 57
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 57
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 62.5мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.97мс)
   📊 Количество ключей в Redis: 198
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 59.57мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 57
   ✅ Активных ботов: 57
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 57
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 59.57мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 18.62мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 47.9мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 9.24мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 95.31мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 95.31мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.99мс)
   📊 Количество ключей в Redis: 177
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 66.63мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 66.63мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.74мс)
   📊 Количество ключей в Redis: 189
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 47.33мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.71мс)
   📊 Количество ключей в Redis: 182
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 44.87мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.43мс)
   📊 Количество ключей в Redis: 173
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 159.06мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 159.06мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.32мс)
   📊 Количество ключей в Redis: 175
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 44.33мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.39мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 42.93мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.92мс)
   📊 Количество ключей в Redis: 190
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 161.57мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 161.57мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.67мс)
   📊 Количество ключей в Redis: 189
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 96.37мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 96.37мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.12мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 73.16мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 73.16мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.9мс)
   📊 Количество ключей в Redis: 178
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 167.34мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 167.34мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 9.03мс)
   📊 Количество ключей в Redis: 180
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 78.39мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 78.39мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.18мс)
   📊 Количество ключей в Redis: 189
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 42.11мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.38мс)
   📊 Количество ключей в Redis: 184
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 51.04мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 51.04мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.26мс)
   📊 Количество ключей в Redis: 191
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 41.14мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.03мс)
   📊 Количество ключей в Redis: 192
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 49.32мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 10.22мс)
   📊 Количество ключей в Redis: 191
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 66.2мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 66.2мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 15.64мс)
   📊 Количество ключей в Redis: 189
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 84.71мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 84.71мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 10.59мс)
   📊 Количество ключей в Redis: 178
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 80.22мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 80.22мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 34.25мс)
   📊 Количество ключей в Redis: 175
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 63.04мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 63.04мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.59мс)
   📊 Количество ключей в Redis: 175
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 197.36мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 197.36мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.62мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 120.47мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 120.47мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 10.13мс)
   📊 Количество ключей в Redis: 173
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 135.02мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 135.02мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.65мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 126.49мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 126.49мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 60.53мс)
   📊 Количество ключей в Redis: 175
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 180.1мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 180.1мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.8мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 58.66мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 58.66мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 26.48мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 108.95мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 108.95мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.94мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 48.71мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.01мс)
   📊 Количество ключей в Redis: 185
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 166.31мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 166.31мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.81мс)
   📊 Количество ключей в Redis: 179
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 62.93мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 62.93мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.15мс)
   📊 Количество ключей в Redis: 173
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 61.71мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 61.71мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 10.48мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 82.86мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 82.86мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.69мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 63.01мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 63.01мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 12.28мс)
   📊 Количество ключей в Redis: 176
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 99.41мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 99.41мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 19.17мс)
   📊 Количество ключей в Redis: 175
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 55.14мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 55.14мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 11.19мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 90.63мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 90.63мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.39мс)
   📊 Количество ключей в Redis: 176
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 71.53мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 71.53мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 17.37мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 104.74мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 104.74мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 13.06мс)
   📊 Количество ключей в Redis: 180
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 79.89мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 79.89мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.45мс)
   📊 Количество ключей в Redis: 185
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 40.62мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 43.1мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.86мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 36.93мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.4мс)
   📊 Количество ключей в Redis: 198
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 153.5мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 153.5мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.32мс)
   📊 Количество ключей в Redis: 190
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.23мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 22.93мс)
   📊 Количество ключей в Redis: 188
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.83мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.47мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 40.66мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.8мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 41.4мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.54мс)
   📊 Количество ключей в Redis: 172
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.49мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.27мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 147.94мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 147.94мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.79мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 153.32мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 153.32мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.75мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 37.92мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.32мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 152.74мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 152.74мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.15мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 41.76мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.66мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 149.97мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 149.97мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.15мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 36.87мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.81мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 140.41мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 140.41мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.41мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.19мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.78мс)
   📊 Количество ключей в Redis: 182
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 149.37мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 149.37мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.13мс)
   📊 Количество ключей в Redis: 182
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 48.69мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.15мс)
   📊 Количество ключей в Redis: 185
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 138.21мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 138.21мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.59мс)
   📊 Количество ключей в Redis: 187
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 151.92мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 151.92мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.06мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 41.99мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 13.33мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 77.66мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 77.66мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.3мс)
   📊 Количество ключей в Redis: 181
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.2мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.98мс)
   📊 Количество ключей в Redis: 188
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 43.57мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.16мс)
   📊 Количество ключей в Redis: 193
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.35мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.64мс)
   📊 Количество ключей в Redis: 192
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 40.49мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 56MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.08мс)
   📊 Количество ключей в Redis: 192
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 41.2мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.52мс)
   📊 Количество ключей в Redis: 185
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 150.16мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 150.16мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.54мс)
   📊 Количество ключей в Redis: 189
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 148.14мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 148.14мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.13мс)
   📊 Количество ключей в Redis: 191
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 42.93мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 23.71мс)
   📊 Количество ключей в Redis: 190
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 37.47мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.65мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 37.05мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.98мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.49мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.06мс)
   📊 Количество ключей в Redis: 182
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 136мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 136мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.42мс)
   📊 Количество ключей в Redis: 181
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 40.42мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.96мс)
   📊 Количество ключей в Redis: 150
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.69мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 23.47мс)
   📊 Количество ключей в Redis: 152
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 36.76мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.26мс)
   📊 Количество ключей в Redis: 184
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 36.03мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 24.06мс)
   📊 Количество ключей в Redis: 181
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.21мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.85мс)
   📊 Количество ключей в Redis: 176
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.48мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 23.39мс)
   📊 Количество ключей в Redis: 169
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.74мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.61мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.65мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.79мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.36мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.75мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.14мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 23.32мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.57мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.06мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.59мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.68мс)
   📊 Количество ключей в Redis: 169
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.71мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 24.59мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 147.44мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 147.44мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.89мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.85мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5мс)
   📊 Количество ключей в Redis: 178
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.43мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.49мс)
   📊 Количество ключей в Redis: 182
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.97мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.69мс)
   📊 Количество ключей в Redis: 179
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 47.64мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.94мс)
   📊 Количество ключей в Redis: 177
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 55.22мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 55.22мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.59мс)
   📊 Количество ключей в Redis: 181
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.94мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 20.65мс)
   📊 Количество ключей в Redis: 182
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.74мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.18мс)
   📊 Количество ключей в Redis: 181
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.46мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.72мс)
   📊 Количество ключей в Redis: 184
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.04мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.28мс)
   📊 Количество ключей в Redis: 184
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 36.07мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.86мс)
   📊 Количество ключей в Redis: 184
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.02мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.75мс)
   📊 Количество ключей в Redis: 182
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.93мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.1мс)
   📊 Количество ключей в Redis: 171
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.8мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.9мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.91мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.99мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.58мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.73мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.54мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.84мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.02мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.39мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.35мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.58мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.3мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.75мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.64мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 20.44мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.45мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.73мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 32.77мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.42мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.76мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.05мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 37.24мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 12мс)
   📊 Количество ключей в Redis: 187
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.04мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.23мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.54мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.75мс)
   📊 Количество ключей в Redis: 178
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 38.27мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.07мс)
   📊 Количество ключей в Redis: 178
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 35.06мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 22.7мс)
   📊 Количество ключей в Redis: 177
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 152.42мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 152.42мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.47мс)
   📊 Количество ключей в Redis: 180
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 35.92мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.48мс)
   📊 Количество ключей в Redis: 177
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 154.89мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 154.89мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.38мс)
   📊 Количество ключей в Redis: 189
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 150мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 150мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 15.5мс)
   📊 Количество ключей в Redis: 187
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 118.6мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 118.6мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.55мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 157.68мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 157.68мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.51мс)
   📊 Количество ключей в Redis: 187
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 90.1мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 90.1мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.6мс)
   📊 Количество ключей в Redis: 185
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 58.5мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 58.5мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.84мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 48.76мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.4мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 55.61мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 55.61мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 10.81мс)
   📊 Количество ключей в Redis: 182
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 89.5мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 89.5мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.92мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 53.49мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 53.49мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.98мс)
   📊 Количество ключей в Redis: 188
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 51.69мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 51.69мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.57мс)
   📊 Количество ключей в Redis: 188
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 37.43мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.97мс)
   📊 Количество ключей в Redis: 184
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 43.39мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.48мс)
   📊 Количество ключей в Redis: 188
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 171.4мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 171.4мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.25мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 72.34мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 72.34мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.51мс)
   📊 Количество ключей в Redis: 179
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 45.75мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.57мс)
   📊 Количество ключей в Redis: 172
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 150.07мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 150.07мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.39мс)
   📊 Количество ключей в Redis: 186
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 55.95мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 55.95мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.67мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 51.5мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 51.5мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.16мс)
   📊 Количество ключей в Redis: 184
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 43.19мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.06мс)
   📊 Количество ключей в Redis: 185
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 41.68мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.14мс)
   📊 Количество ключей в Redis: 182
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.34мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.41мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 145.33мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 145.33мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 23.66мс)
   📊 Количество ключей в Redis: 173
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 42.55мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.33мс)
   📊 Количество ключей в Redis: 185
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 42.42мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.04мс)
   📊 Количество ключей в Redis: 185
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 35.21мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.04мс)
   📊 Количество ключей в Redis: 187
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 36.03мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 21.19мс)
   📊 Количество ключей в Redis: 185
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 47.22мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 21.33мс)
   📊 Количество ключей в Redis: 183
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 147.8мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 147.8мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.88мс)
   📊 Количество ключей в Redis: 190
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 134.41мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 134.41мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.89мс)
   📊 Количество ключей в Redis: 188
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 134.85мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 134.85мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.83мс)
   📊 Количество ключей в Redis: 191
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 41.86мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 22.53мс)
   📊 Количество ключей в Redis: 170
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 148.18мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 148.18мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 8.26мс)
   📊 Количество ключей в Redis: 181
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.76мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.91мс)
   📊 Количество ключей в Redis: 181
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 35.62мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.99мс)
   📊 Количество ключей в Redis: 174
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 34.07мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 7.74мс)
   📊 Количество ключей в Redis: 191
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 35.1мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 18.89мс)
   📊 Количество ключей в Redis: 190
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 33.08мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 4.77мс)
   📊 Количество ключей в Redis: 178
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 136.65мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Медленный отклик БД: 136.65мс
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.92мс)
   📊 Количество ключей в Redis: 196
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 35.93мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 5.2мс)
   📊 Количество ключей в Redis: 194
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 39.33мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 40
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
🔍 ДИАГНОСТИКА ПРОИЗВОДИТЕЛЬНОСТИ СИСТЕМЫ БОТОВ
===============================================

🖥️ Проверка системных ресурсов...
   💾 Текущее использование памяти: 58MB
   📊 Лимит памяти: 512M
   ⏱️ Максимальное время выполнения: 0s
🔴 Проверка Redis...
   ✅ Redis доступен (время отклика: 6.05мс)
   📊 Количество ключей в Redis: 194
🗄️ Проверка базы данных...
   ✅ БД доступна (время отклика: 36.52мс)
🤖 Проверка данных ботов...
   📊 Всего ботов: 58
   ✅ Активных ботов: 58
   💀 Мертвых ботов: 0
   👑 Ботов от админа: 58
   🏰 Активных аванпостов: 12
⚙️ Проверка конфигурации...
   🤖 Максимум ботов за тик: 25
   📦 Размер пакета: 10
   💾 Лимит памяти: 64MB
📇 Проверка индексов базы данных...

❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ ПРОИЗВОДИТЕЛЬНОСТИ

💡 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ:
   • Низкий лимит времени выполнения: 0s
   • Много активных аванпостов: 12
   • Не удалось проверить индексы для bots: SQLSTATE[42601]: Syntax error: 7 ОШИБКА:  ошибка синтаксиса (примерное положение: "FROM")
LINE 1: SHOW INDEX FROM bots
                   ^ (Connection: pgsql, SQL: SHOW INDEX FROM bots)
